-- Q2 

select date(dwload_ts) load_date, count(1) K from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557

select date(dwload_ts) load_date, count(1) K from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557

with cte_prd_data as (
select distinct account_id, system_serial_number, qtr, (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR where date(dwload_ts) is null
)
-- ;
-------------------------------------------------------------------------------------------------------------------------  
,cte_sbx_data as (
select distinct account_id, system_serial_number, qtr, (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS where date(dwload_ts) is null
)
select prd.account_id, prd.system_serial_number, prd.qtr, round(prd.tot_val, 2) prd_val, round(sbx.tot_val, 2) sbx_val, round((prd.tot_val-sbx.tot_val),2) as variance
from cte_prd_data prd
inner join cte_sbx_data sbx
on prd.account_id = sbx.account_id and prd.system_serial_number = sbx.system_serial_number and prd.qtr = sbx.qtr
where prd_val<>sbx_val and prd.qtr='20252'
order by abs(variance) desc
;

-- result  6k rows has minor variance q2 data

-- example few rows

-- 11633	SQ0805	20252	76.31	104.81	-28.5
-- 15008	SQ0301	20252	1552.58	1534.21	18.36
-- 119816	SQ0813	20252	81.06	99.06	-18
-- 11646	SL0344	20252	80.68	98.68	-18
-- 16115	SQ0822	20252	83.27	101.27	-18
-- 20335	SK2921	20252	1109.76	1096.09	13.67
-- 11513	SQ0820	20252	454.74	467.49	-12.75



select 'sbx' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where date(dwload_ts) is null and account_id='10829' and system_serial_number='SK5188' and qtr='20252'
UNION ALL
select 'prd' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where date(dwload_ts) is null and account_id='10829' and system_serial_number='SK5188' and qtr='20252'
;

-- result:  

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- prd	SK5188	20252	Mercy Medical Center Merced	10829	001o000000iySgHAAU	da Vinci Xi	2021-12-22	F	System		United States		15	65	TRUE	4.522	3.324577739	0.**********	3.01	2.01460737	3.707222222	2.20140777	0.09930059524	0.08687455809	3.97202381	3.474982324		0.**********	0.**********	0	63	50	10	2	1	2	0	0	0	0	0	0	0	3	0.933333	0.866667	0.533333	0.238095	123.3	100.5	48.104	0.39	142	142	68	0.479	118.625	100.5	40.339	0.34	149	149	43	0.289	108.5	93	33.812	0.312									0.**********	0.**********	0.03174603175	0.0**********	0.03174603175								0.04761904762	5	15	1900-01-01 11:46:16.000	1900-01-01 16:17:36.000	75.291682349	Monika Noghli	Bryan Botma	Scott Floyd	Carmo Rodrigues		
-- sbx	SK5188	20252	Mercy Medical Center Merced	10829	001o000000iySgHAAU	da Vinci Xi	2021-12-22	F	System		United States		15	63	TRUE	4.522	3.211847236	0.**********	3.01	1.946295627	3.707222222	2.126761958	0.09930059524	0.08687455809	3.97202381	3.474982324	0.**********	0.**********	0.**********	0	63	48	10	2	1	2	0	0	0	0	0	0	0	3	0.933333	0.866667	0.533333	0.238095	123.3	100.5	48.104	0.39	142	142	68	0.479	118.625	100.5	40.339	0.34	149	149	43	0.289	108.5	93	33.812	0.312									0.761905	0.15873	0.031746	0.015873	0.031746								0.047619	5	15	1900-01-01 11:46:16.000	1900-01-01 16:17:36.000	75.291682349	Monika Noghli	Bryan Botma	Scott Floyd	Carmo Rodrigues			


-- comparing 2025-Q2 data which is fine without any variance as seen above results, we need to check for Q1 data as well.
-- since 2025-Q2 is completed, there should not be any gap between prod and SBX even if the load date is different,
-- to check for individual case
-- using above query
-- if we find any gaps because of load time gap for 2025-Q2, we can take 2025-Q1 data for validation


-- Q1 data validation

-- Production data load dates
select date(dwload_ts) load_date, count(1) K 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557



-- Sandbox data load dates
select date(dwload_ts) load_date, count(1) K 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557


with cte_prd_data as (
select distinct account_id, system_serial_number, qtr, 
(NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where qtr = '20251'  -- Q1 2025
)
,cte_sbx_data as (
select distinct account_id, system_serial_number, qtr, 
(NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where qtr = '20251'  -- Q1 2025
)
select prd.account_id, prd.system_serial_number, prd.qtr, 
       round(prd.tot_val, 2) prd_val, 
       round(sbx.tot_val, 2) sbx_val, 
       round((prd.tot_val-sbx.tot_val),2) as variance
from cte_prd_data prd
inner join cte_sbx_data sbx
  on prd.account_id = sbx.account_id 
  and prd.system_serial_number = sbx.system_serial_number 
  and prd.qtr = sbx.qtr
where prd_val <> sbx_val 
  and prd.qtr = '20251'  -- Q1 2025
order by abs(variance) desc;

-- Result

-- 5.8k rows

-- sample rows from result

-- 15675	SK2160	20251	79.62	98.95	-19.33
-- 16941	SK6632	20251	75.56	94.89	-19.33
-- 12143	SQ0603	20251	77.01	96.34	-19.33
-- 10639	SK6779	20251	1509.79	1524.22	-14.43
-- 117778	SK0075	20251	78.3	92.23	-13.93
-- 15778	SK0100	20251	571.48	584.77	-13.28
-- 117778	SK1863	20251	81.19	92.01	-10.82



-- Replace with actual account_id and system_serial_number from Q1 2025 data
select 'sbx' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where account_id = '117891'  -- Replace with actual value
  and system_serial_number = 'SK5066'  -- Replace with actual value
  and qtr = '20251'
UNION ALL
select 'prd' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where account_id = '117891'  -- Replace with actual value
  and system_serial_number = 'SK5066'  -- Replace with actual value
  and qtr = '20251';


  -- results 

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- sbx	SK5066	20251	Mount Carmel Grove City	117891	0011J00001MKTIMQA5	da Vinci Xi	2021-11-11	F	System		United States		39	61	TRUE	4.367948718	2.382744786	0.9206802964	2.468376068	1.768136765	3.808410256	1.863293585	0.2652285714	0.1384297884	10.609142857	5.537191537	1.901807081	1.255271217	0.63691318	0	61	22	19	13	7	0	0	0	0	0	0	0	0	7	0.897436	0.717949	0.307692	0.639344	98.481	67	66.463	0.675	119.077	110	83.45	0.701	79.357	66	35.96	0.453	79.357	66	35.96	0.453													0.360656	0.311475	0.213115	0.114754									0.114754	20	39	1900-01-01 10:19:35.000	1900-01-01 14:41:41.000	18.836215271	Michael Rabin	Benjamin Stein	Andrea Crist	Nicholas McCreary		
-- prd	SK5066	20251	Mount Carmel Grove City	117891	0011J00001MKTIMQA5	da Vinci Xi	2021-11-11	F	System		United States		39	61	TRUE	4.367948718	2.413893099	0.9206802964	2.468376068	1.791250645	3.808410256	1.887651397	0.2652285714	0.1384297884	10.609142857	5.537191537		1.255271217	0.63691318	0	61	22	19	13	7	0	0	0	0	0	0	0	0	7	0.897436	0.717949	0.307692	0.639344	98.481	67	66.463	0.675	119.077	110	83.45	0.701	79.357	66	35.96	0.453	79.357	66	35.96	0.453													0.3606557377	0.3114754098	0.2131147541	0.1147540984									0.1147540984	20	39	1900-01-01 10:19:35.000	1900-01-01 14:41:41.000	18.832556333	Michael Rabin	Benjamin Stein	Andrea Crist	Nicholas McCreary			


-- First, let's check record counts by quarter to understand the data distribution

-- Check Q1 2025 record counts in both tables
select 'BACKUP_QTR' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20251'
group by qtr
UNION ALL
select 'CURRENT' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20251'
group by qtr;

-- results

-- SOURCE	QTR	RECORD_COUNT
-- BACKUP_QTR	20251	7041
-- CURRENT	20251	7041

select 'BACKUP_QTR' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20252'
group by qtr
UNION ALL
select 'CURRENT' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20252'
group by qtr;

-- results

-- SOURCE	QTR	RECORD_COUNT
-- BACKUP_QTR	20252	7166
-- CURRENT	20252	7166

-- Check for specific Q1 2025 variance example
select 'sbx' source, account_id, system_serial_number, qtr,
       total_operating_hours_mean, total_operating_hours_std,
       proc_days_0, proc_days_1, proc_days_2,
       total_gaps_avg, total_gaps_std, average_turnover_time_mins
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where account_id = '11579' and system_serial_number = 'SQ0093' and qtr = '20251'
UNION ALL
select 'prd' source, account_id, system_serial_number, qtr,
       total_operating_hours_mean, total_operating_hours_std,
       proc_days_0, proc_days_1, proc_days_2,
       total_gaps_avg, total_gaps_std, average_turnover_time_mins
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where account_id = '11579' and system_serial_number = 'SQ0093' and qtr = '20251';

-- Results

-- SOURCE	ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	QTR	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	TOTAL_GAPS_AVG	TOTAL_GAPS_STD	AVERAGE_TURNOVER_TIME_MINS
-- sbx	11579	SQ0093	20251	8.*********	4.*********	1	0	11	71.026	38.878	70.5
-- prd	11579	SQ0093	20251	8.*********	4.********	1	0	11	71.026	38.878	70.5

-- DETAILED COLUMN-BY-COLUMN COMPARISON FOR DEBUGGING
-- This will help identify which specific columns are causing the variance
with prd_data as (
  select account_id, system_serial_number, qtr,
         total_operating_hours_mean, total_operating_hours_std,
         percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
         total_gaps_avg, total_gaps_std, average_turnover_time_mins,
         proc_days_0, proc_days_1, proc_days_2, proc_days_3
  from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
  where account_id = '11579' and system_serial_number = 'SQ0093' and qtr = '20251'
),
sbx_data as (
  select account_id, system_serial_number, qtr,
         total_operating_hours_mean, total_operating_hours_std,
         percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
         total_gaps_avg, total_gaps_std, average_turnover_time_mins,
         proc_days_0, proc_days_1, proc_days_2, proc_days_3
  from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
  where account_id = '11579' and system_serial_number = 'SQ0093' and qtr = '20251'
)
select
  'TOTAL_OPERATING_HOURS_MEAN' as column_name,
  p.total_operating_hours_mean as prd_value,
  s.total_operating_hours_mean as sbx_value,
  (p.total_operating_hours_mean - s.total_operating_hours_mean) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'TOTAL_OPERATING_HOURS_STD' as column_name,
  p.total_operating_hours_std as prd_value,
  s.total_operating_hours_std as sbx_value,
  (p.total_operating_hours_std - s.total_operating_hours_std) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'PERCENTAGE_SYSTEM_USED_WEEKLY_AVG' as column_name,
  p.percentage_system_used_weekly_avg as prd_value,
  s.percentage_system_used_weekly_avg as sbx_value,
  (p.percentage_system_used_weekly_avg - s.percentage_system_used_weekly_avg) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'TOTAL_GAPS_AVG' as column_name,
  p.total_gaps_avg as prd_value,
  s.total_gaps_avg as sbx_value,
  (p.total_gaps_avg - s.total_gaps_avg) as variance
from prd_data p, sbx_data s
where abs(p.total_gaps_avg - s.total_gaps_avg) > 0.01
order by abs(variance) desc;


-- Results

-- COLUMN_NAME	PRD_VALUE	SBX_VALUE	VARIANCE
-- TOTAL_OPERATING_HOURS_STD	4.********	4.*********	0.***********
-- PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	0.**********	0.**********	-1.110223025e-16
-- TOTAL_OPERATING_HOURS_MEAN	8.*********	8.*********	0

-- STEP 1: Check Operating Hours Metrics (most likely culprits)
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_operating_hours_mean, total_operating_hours_std,
           utilization_day_mean, afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std,
           or_duration_per_day_mean, or_duration_per_day_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20251'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_operating_hours_mean, total_operating_hours_std,
           utilization_day_mean, afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std,
           or_duration_per_day_mean, or_duration_per_day_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20251'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.total_operating_hours_mean,0) - NVL(s.total_operating_hours_mean,0)) as var_op_hours_mean,
       ABS(NVL(p.total_operating_hours_std,0) - NVL(s.total_operating_hours_std,0)) as var_op_hours_std,
       ABS(NVL(p.utilization_day_mean,0) - NVL(s.utilization_day_mean,0)) as var_utilization,
       ABS(NVL(p.afternoon_hour_on_or_day_mean,0) - NVL(s.afternoon_hour_on_or_day_mean,0)) as var_afternoon_mean,
       ABS(NVL(p.afternoon_hour_on_or_day_std,0) - NVL(s.afternoon_hour_on_or_day_std,0)) as var_afternoon_std,
       ABS(NVL(p.or_duration_per_day_mean,0) - NVL(s.or_duration_per_day_mean,0)) as var_or_duration_mean,
       ABS(NVL(p.or_duration_per_day_std,0) - NVL(s.or_duration_per_day_std,0)) as var_or_duration_std
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.total_operating_hours_mean,0) - NVL(s.total_operating_hours_mean,0)) > 0.01
    OR ABS(NVL(p.total_operating_hours_std,0) - NVL(s.total_operating_hours_std,0)) > 0.01
    OR ABS(NVL(p.afternoon_hour_on_or_day_std,0) - NVL(s.afternoon_hour_on_or_day_std,0)) > 0.01
    OR ABS(NVL(p.or_duration_per_day_std,0) - NVL(s.or_duration_per_day_std,0)) > 0.01)
ORDER BY (var_op_hours_mean + var_op_hours_std + var_afternoon_std + var_or_duration_std) DESC
LIMIT 20;

-- results

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	VAR_OP_HOURS_MEAN	VAR_OP_HOURS_STD	VAR_UTILIZATION	VAR_AFTERNOON_MEAN	VAR_AFTERNOON_STD	VAR_OR_DURATION_MEAN	VAR_OR_DURATION_STD
-- 11156	SK6584	0	1.*********	0	0	1.*********	0	1.*********
-- 11156	SK6584	0	1.*********	0	0	1.*********	0	1.*********
-- 11156	SK6584	0	1.*********	0	0	1.*********	0	1.*********
-- 11156	SK6584	0	1.*********	0	0	1.*********	0	1.*********
-- 118759	SL0539	8.881784197e-16	0.**********	0	0	1.*********	8.881784197e-16	1.*********
-- 12241	SK8280	0	1.*********	0	0	0.**********	0	1.*********
-- 122966	SK6313	0	1.*********	1.110223025e-16	0	1.*********	0	0.**********
-- 122966	SK6313	0	1.*********	1.110223025e-16	0	1.*********	0	0.**********
-- 122966	SK6313	0	1.*********	1.110223025e-16	0	1.*********	0	0.**********
-- 122966	SK6313	0	1.*********	1.110223025e-16	0	1.*********	0	0.**********
-- 115467	SQ0593	0	1.*********	0	0	0.**********	0	0.*********
-- 15964	SK0733	0	0.**********	0	0	1.*********	0	0.**********
-- 11515	SL0607	0	0.8215606079	0	0	0.5997591011	0	0.6496152076
-- 118821	SK7034	0	0.**********	0	0	0.**********	0	0.**********
-- 118821	SK7034	0	0.**********	0	0	0.**********	0	0.**********
-- 118821	SK7034	0	0.**********	0	0	0.**********	0	0.**********
-- 118821	SK7034	0	0.**********	0	0	0.**********	0	0.**********
-- 10003	SK8200	0	0.**********	0	0	0.**********	0	0.**********
-- 21158	SK1295	0	0.**********	0	0	0.**********	0	0.**********
-- 10107	SP0103	0	0.**********	0	0	0.**********	8.881784197e-16	0.**********

-- STEP 2: Check Gap/Turnover Metrics
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_gaps_avg, total_gaps_std, average_turnover_time_mins, std_turnover_time_mins,
           gaps_2_proc_days_std, gaps_3_proc_days_std, gaps_4_proc_days_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20251'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_gaps_avg, total_gaps_std, average_turnover_time_mins, std_turnover_time_mins,
           gaps_2_proc_days_std, gaps_3_proc_days_std, gaps_4_proc_days_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20251'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.total_gaps_avg,0) - NVL(s.total_gaps_avg,0)) as var_gaps_avg,
       ABS(NVL(p.total_gaps_std,0) - NVL(s.total_gaps_std,0)) as var_gaps_std,
       ABS(NVL(p.average_turnover_time_mins,0) - NVL(s.average_turnover_time_mins,0)) as var_turnover_avg,
       ABS(NVL(p.std_turnover_time_mins,0) - NVL(s.std_turnover_time_mins,0)) as var_turnover_std,
       ABS(NVL(p.gaps_2_proc_days_std,0) - NVL(s.gaps_2_proc_days_std,0)) as var_gaps_2_std,
       ABS(NVL(p.gaps_3_proc_days_std,0) - NVL(s.gaps_3_proc_days_std,0)) as var_gaps_3_std
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.total_gaps_std,0) - NVL(s.total_gaps_std,0)) > 0.01
    OR ABS(NVL(p.std_turnover_time_mins,0) - NVL(s.std_turnover_time_mins,0)) > 0.01
    OR ABS(NVL(p.gaps_2_proc_days_std,0) - NVL(s.gaps_2_proc_days_std,0)) > 0.01)
ORDER BY (var_gaps_std + var_turnover_std + var_gaps_2_std + var_gaps_3_std) DESC
LIMIT 20;

-- results got only 4 rows

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	VAR_GAPS_AVG	VAR_GAPS_STD	VAR_TURNOVER_AVG	VAR_TURNOVER_STD	VAR_GAPS_2_STD	VAR_GAPS_3_STD
-- 15886	SQ0082	0.395	0.615	0	0	1.358	0
-- 10639	SK6779	0.392	0.759	1.366	0.243	0	0
-- 119729	SK8026	0.271	0.069	0.556	0.241	0	0.072
-- 11579	SK1081	0.194	0.161	0.116	0.111	0	0

-- STEP 3: Deep dive into gap calculation differences
-- Check if the raw gap data itself is different between production and sandbox

-- First, let's see if there are different numbers of gap records
SELECT 
    'PRODUCTION_GAPS' as source,
    COUNT(*) as gap_count,
    AVG(gap_minutes) as avg_gap,
    STDDEV_POP(gap_minutes) as std_gap
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
WHERE p.account_id = '117891' 
  AND p.system_serial_number = 'SK5066' 
  AND cal.calyearqtr = '20251'
  AND p.gap_minutes IS NOT NULL
  AND p.gap_minutes > 0

UNION ALL

-- Check what your optimized code would calculate
SELECT 
    'SANDBOX_APPROACH' as source,
    COUNT(*) as gap_count,
    AVG(gap_minutes) as avg_gap,
    STDDEV_POP(gap_minutes) as std_gap
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
WHERE p.account_id = '117891' 
  AND p.system_serial_number = 'SK5066' 
  AND cal.calyearqtr = '20251'
  AND p.gap_minutes IS NOT NULL
  AND p.gap_minutes > 0;

--   result
-- Error: invalid identifier 'GAP_MINUTES' (line 396)
  

-- STEP 3: Check the actual column names in the procedure data table first
SELECT column_name 
FROM EDWSBX.INFORMATION_SCHEMA.COLUMNS 
WHERE table_name = 'GENESIS_PROCEDURE_DATA_HISTORICAL' 
  AND table_schema = 'TRAINING'
  AND column_name LIKE '%GAP%'
ORDER BY column_name;

-- no result

-- Alternative: Check a sample record to see available columns
SELECT * 
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL 
WHERE account_id = '117891' 
  AND system_serial_number = 'SK5066' 
LIMIT 1;

-- result

-- ACCOUNT_ID	ACCOUNT_NAME	SYSTEM_SERIAL_NUMBER	PROCEDURE_NUMBER	PROCEDURE_DATE_LOCAL	START_TIME_LOCAL	PROCEDURE_DURATION	SURGEON_NAME	SURGEON_ID	SURGEON_SPECIALITY	SYSTEM_LOCATION_TYPE	BUSINESS_CATEGORY	PROCEDURE_NAME	SUBJECT	CAL_YEAR_QTR	COUNTRY	START_TIME_WITH_DATE	HOUR_OF_PROCEDURE_START	END_TIME_WITH_DATE	HOUR_OF_PROCEDURE_END	BUSINESS_DAY_DATE	SYSTEM_NAME	PROCEDURE_COUNT	PROCEDURE_DAYS	DWLOAD_TS	DAY_OF_WEEK
-- 117891	Mount Carmel Grove City	SK5066	C24357460	2025-01-07 00:00:00.000	10:46 AM	5400	Irina Arp	318804	GEN:GEN		General Surgery	Inguinal Hernia - Bilateral	Inguinal Hernia	20251	United States	2025-01-07 10:46:00.000	10	2025-01-07 12:16:00.000	12	2025-01-07	SK5066	2	2		Tuesday


SELECT 
    'PROCEDURE_COUNT_CHECK' as check_type,
    COUNT(*) as total_procedures,
    COUNT(DISTINCT procedure_date_local) as distinct_days,
    MIN(procedure_date_local) as first_date,
    MAX(procedure_date_local) as last_date
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
WHERE p.account_id = '117891' 
  AND p.system_serial_number = 'SK5066' 
  AND cal.calyearqtr = '20251'
ORDER BY check_type;

-- result

-- CHECK_TYPE	TOTAL_PROCEDURES	DISTINCT_DAYS	FIRST_DATE	LAST_DATE
-- PROCEDURE_COUNT_CHECK	4818	39	2025-01-07 00:00:00.000	2025-03-28 00:00:00.000

-- Simulate the gap calculation to see what's different
WITH procedure_data AS (
    SELECT 
        account_id, system_serial_number, procedure_date_local,
        start_time_with_date, end_time_with_date,
        ROW_NUMBER() OVER (PARTITION BY account_id, system_serial_number ORDER BY start_time_with_date) as proc_seq
    FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
    JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
    WHERE p.account_id = '117891' 
      AND p.system_serial_number = 'SK5066' 
      AND cal.calyearqtr = '20251'
),
gaps_calculated AS (
    SELECT *,
        LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date) as next_start_time,
        LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date) as next_procedure_date,
        CASE 
            WHEN procedure_date_local = LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date)
            THEN DATEDIFF('minute', end_time_with_date, LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date))
            ELSE NULL 
        END as gap_minutes
    FROM procedure_data
)
SELECT 
    COUNT(*) as total_gaps,
    COUNT(gap_minutes) as valid_gaps,
    AVG(gap_minutes) as avg_gap,
    STDDEV_POP(gap_minutes) as std_gap
FROM gaps_calculated
WHERE gap_minutes > 0;

-- results

-- TOTAL_GAPS	VALID_GAPS	AVG_GAP	STD_GAP
-- 27	27	98.481481	66.*********

-- STEP 4: Check Weekly Features (potential major contributor)
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
           hours_system_used_weekly_avg, hours_system_used_weekly_std,
           weekly_system_used_std_median
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20251'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
           hours_system_used_weekly_avg, hours_system_used_weekly_std,
           weekly_system_used_std_median
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20251'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.percentage_system_used_weekly_avg,0) - NVL(s.percentage_system_used_weekly_avg,0)) as var_weekly_pct_avg,
       ABS(NVL(p.percentage_system_used_weekly_std,0) - NVL(s.percentage_system_used_weekly_std,0)) as var_weekly_pct_std,
       ABS(NVL(p.hours_system_used_weekly_avg,0) - NVL(s.hours_system_used_weekly_avg,0)) as var_weekly_hrs_avg,
       ABS(NVL(p.hours_system_used_weekly_std,0) - NVL(s.hours_system_used_weekly_std,0)) as var_weekly_hrs_std,
       ABS(NVL(p.weekly_system_used_std_median,0) - NVL(s.weekly_system_used_std_median,0)) as var_weekly_median
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.percentage_system_used_weekly_std,0) - NVL(s.percentage_system_used_weekly_std,0)) > 0.01
    OR ABS(NVL(p.hours_system_used_weekly_std,0) - NVL(s.hours_system_used_weekly_std,0)) > 0.01
    OR ABS(NVL(p.weekly_system_used_std_median,0) - NVL(s.weekly_system_used_std_median,0)) > 0.01)
ORDER BY (var_weekly_pct_std + var_weekly_hrs_std + var_weekly_median) DESC
LIMIT 20;

-- results

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	VAR_WEEKLY_PCT_AVG	VAR_WEEKLY_PCT_STD	VAR_WEEKLY_HRS_AVG	VAR_WEEKLY_HRS_STD	VAR_WEEKLY_MEDIAN
-- 13309	SQ0125	0	0	0	3.552713679e-15	8.*********
-- 13425	SQ0284	1.110223025e-16	1.665334537e-16	0	5.329070518e-15	8.*********
-- 12727	SQ0228	0	1.110223025e-16	0	1.776356839e-15	7.*********
-- 15343	SQ0202	0	2.775557562e-16	7.105427358e-15	1.243449788e-14	7.********
-- 16451	SQ0296	0	1.110223025e-16	0	1.421085472e-14	7.*********
-- 13104	SQ0326	0	2.775557562e-16	3.552713679e-15	1.065814104e-14	7.*********
-- 14231	SQ0204	0	5.551115123e-17	7.105427358e-15	3.552713679e-15	6.*********
-- 10003	SQ0448	1.110223025e-16	1.110223025e-16	0	0	6.*********
-- 16342	SQ0240	1.110223025e-16	1.110223025e-16	7.105427358e-15	8.881784197e-15	4.********
-- 12460	SQ0310	1.110223025e-16	1.665334537e-16	0	8.881784197e-15	4.*********
-- 11504	SQ0066	0	3.330669074e-16	0	1.065814104e-14	4.*********
-- 14029	SQ0308	0	1.110223025e-16	0	2.131628207e-14	4.*********
-- 14910	SK6633	1.110223025e-16	3.885780586e-16	3.552713679e-15	8.881784197e-15	4.11644601
-- 10215	SP0024	0	5.551115123e-17	3.552713679e-15	7.105427358e-15	3.860812425
-- 10066	SP0031	5.551115123e-17	2.775557562e-17	1.776356839e-15	0	3.*********
-- 12752	SP0173	5.551115123e-17	0	0	1.776356839e-15	3.*********
-- 10053	SK6339	0	8.326672685e-17	0	0	3.*********
-- 11149	SK5626	5.551115123e-17	5.551115123e-17	0	5.329070518e-15	3.*********
-- 10848	SQ0460	1.110223025e-16	5.551115123e-17	3.552713679e-15	5.329070518e-15	3.*********
-- 16649	SP0327	0	1.665334537e-16	3.552713679e-15	1.776356839e-15	3.*********


-- STEP 5: Check Procedure Day Counts (could be major)
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20251'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20251'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) as var_proc_days_0,
       ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) as var_proc_days_1,
       ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) as var_proc_days_2,
       ABS(NVL(p.proc_days_3,0) - NVL(s.proc_days_3,0)) as var_proc_days_3,
       ABS(NVL(p.plus_proc_days_3_plus_percentage,0) - NVL(s.plus_proc_days_3_plus_percentage,0)) as var_3plus_pct
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) > 0
    OR ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) > 0
    OR ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) > 0)
ORDER BY (var_proc_days_0 + var_proc_days_1 + var_proc_days_2 + var_proc_days_3) DESC
LIMIT 20;

-- results 0 rows

-- Check the source data for both procedures
SELECT 
    ps.casenumber,
    ps.systemname,
    ps.accountid,
    procs.installedbaseguid,
    ib.systemserialnum,
    CASE 
        WHEN ps.systemname IS NULL THEN 'NULL_IN_PROCEDURESUMMARY'
        WHEN procs.installedbaseguid IS NULL THEN 'NULL_INSTALLEDBASEGUID'
        WHEN ib.systemserialnum IS NULL THEN 'NULL_IN_INSTALLBASE'
        ELSE 'DATA_COMPLETE'
    END as data_status
FROM EDW.PROCEDURES.VW_PROCEDURESUMMARY ps
LEFT JOIN EDW.procedures.vw_procedures procs ON procs.casenumber = ps.casenumber
LEFT JOIN EDW.master.vw_installbase ib ON procs.installedbaseguid = ib.installbaseguid
WHERE ps.casenumber IN ('C24349211', 'C24750659')
ORDER BY ps.casenumber;

-- result

-- CASENUMBER	SYSTEMNAME	ACCOUNTID	INSTALLEDBASEGUID	SYSTEMSERIALNUM	DATA_STATUS
-- C24349211	SQ0301	15008	a1SUi000002ZCpxMAG	SQ0301	DATA_COMPLETE
-- C24750659	SK6602	11336	a1S5c000006M0FfEAK	SK6602	DATA_COMPLETE

-- Compare the two procedures between backup and sandbox tables
SELECT 
    'BACKUP' as table_source,
    procedure_number,
    system_serial_number,
    account_id,
    cal_year_qtr
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL_BKUP_QTR
WHERE procedure_number IN ('C24349211', 'C24750659')

UNION ALL

SELECT 
    'SANDBOX' as table_source,
    procedure_number,
    system_serial_number,
    account_id,
    cal_year_qtr
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
WHERE procedure_number IN ('C24349211', 'C24750659')
ORDER BY procedure_number, table_source;

-- results

-- TABLE_SOURCE	PROCEDURE_NUMBER	SYSTEM_SERIAL_NUMBER	ACCOUNT_ID	CAL_YEAR_QTR
-- BACKUP	C24349211	SQ0301	15008	20251
-- SANDBOX	C24349211	SQ0301	15008	20251
-- BACKUP	C24750659	SK6602	11336	20251
-- SANDBOX	C24750659	SK6602	11336	20251

-- Check if these procedures meet the filtering criteria
SELECT 
    procedure_number,
    system_serial_number,
    procedure_duration,
    start_time_local,
    CASE 
        WHEN procedure_duration IS NULL THEN 'FILTERED_OUT_NULL_DURATION'
        WHEN system_serial_number IS NULL THEN 'NULL_SYSTEM_NAME'
        WHEN LENGTH(TRIM(start_time_local)) != 8 THEN 'INVALID_START_TIME'
        ELSE 'SHOULD_BE_INCLUDED'
    END as processing_status
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
WHERE procedure_number IN ('C24349211', 'C24750659');

-- results

-- PROCEDURE_NUMBER	SYSTEM_SERIAL_NUMBER	PROCEDURE_DURATION	START_TIME_LOCAL	PROCESSING_STATUS
-- C24750659	SK6602	4500	02:09 PM	SHOULD_BE_INCLUDED
-- C24349211	SQ0301	9480	01:50 PM	SHOULD_BE_INCLUDED

-- Check both columns for the problematic procedure
SELECT 
    procedure_number,
    system_name,           -- This column (likely NULL)
    system_serial_number,  -- This column (likely populated)
    account_id,
    procedure_date_local
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
WHERE procedure_number = 'C24750659';

-- result

-- PROCEDURE_NUMBER	SYSTEM_NAME	SYSTEM_SERIAL_NUMBER	ACCOUNT_ID	PROCEDURE_DATE_LOCAL
-- C24750659	null 	SK6602	11336	2025-02-17 00:00:00.000

-- Compare with backup table
SELECT 
    procedure_number,
    system_name,           -- Check if this differs
    system_serial_number,  -- Check if this differs
    account_id,
    procedure_date_local
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL_BKUP_QTR
WHERE procedure_number = 'C24750659';

-- result

-- PROCEDURE_NUMBER	SYSTEM_NAME	SYSTEM_SERIAL_NUMBER	ACCOUNT_ID	PROCEDURE_DATE_LOCAL
-- C24750659	null 	SK6602	11336	2025-02-17 00:00:00.000


-- Check both columns for the problematic procedure
SELECT 
    procedure_number,
    system_name,           -- This column (likely NULL)
    system_serial_number,  -- This column (likely populated)
    account_id,
    procedure_date_local
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
WHERE procedure_number = 'C24349211';

-- result

-- PROCEDURE_NUMBER	SYSTEM_NAME	SYSTEM_SERIAL_NUMBER	ACCOUNT_ID	PROCEDURE_DATE_LOCAL
-- C24349211	SQ0301	SQ0301	15008	2025-01-06 00:00:00.000

-- Compare with backup table
SELECT 
    procedure_number,
    system_name,           -- Check if this differs
    system_serial_number,  -- Check if this differs
    account_id,
    procedure_date_local
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL_BKUP_QTR
WHERE procedure_number = 'C24349211';

-- result

-- PROCEDURE_NUMBER	SYSTEM_NAME	SYSTEM_SERIAL_NUMBER	ACCOUNT_ID	PROCEDURE_DATE_LOCAL
-- C24349211	NULL	SQ0301	15008	2025-01-06 00:00:00.000

-- Check the underlying PROC_DAYS values for system SQ0093
SELECT 
    system_serial_number,
    qtr,
    proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
    proc_days_6, proc_days_7, proc_days_8, proc_days_9, proc_days_10, proc_days_11,
    total_business_days
FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
WHERE system_serial_number = 'SQ0093' AND qtr = '20251';

-- result

-- SYSTEM_SERIAL_NUMBER	QTR	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	TOTAL_BUSINESS_DAYS
-- SQ0093	20251	1	0	11	25	12	9	2	1	0	0	0	0	61

-- Compare with backup
SELECT 
    system_serial_number,
    qtr,
    proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
    proc_days_6, proc_days_7, proc_days_8, proc_days_9, proc_days_10, proc_days_11,
    total_business_days
FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
WHERE system_serial_number = 'SQ0093' AND qtr = '20251';

-- result

-- SYSTEM_SERIAL_NUMBER	QTR	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	TOTAL_BUSINESS_DAYS
-- SQ0093	20251	1	0	11	25	12	9	2	1	0	0	0	0	61

-- Check what the production code actually produces for these percentages
SELECT 
    system_serial_number,
    qtr,
    proc_days_1_percentage,
    proc_days_8_percentage, 
    proc_days_9_percentage,
    proc_days_10_percentage,
    proc_days_11_percentage
FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
WHERE system_serial_number = 'SQ0093' AND qtr = '20251';

-- results

-- SYSTEM_SERIAL_NUMBER	QTR	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE
-- SQ0093	20251			

-- Check if optimized code produces ANY percentage values for columns that should have them
SELECT 
    system_serial_number,
    qtr,
    proc_days_0_percentage,  -- Should be ~0.0164
    proc_days_2_percentage,  -- Should be ~0.1803  
    proc_days_3_percentage,  -- Should be ~0.4098
    proc_days_4_percentage,  -- Should be ~0.1967
    proc_days_5_percentage,  -- Should be ~0.1475
    proc_days_6_percentage,  -- Should be ~0.0328
    proc_days_7_percentage   -- Should be ~0.0164
FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
WHERE system_serial_number = 'SQ0093' AND qtr = '20251';

-- results

-- SYSTEM_SERIAL_NUMBER	QTR	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE
-- SQ0093	20251	0.016393	0.180328	0.409836	0.196721	0.147541	0.032787	0.016393

-- Debug: Check if the underlying PROC_DAYS and business days data exists
SELECT 
    system_serial_number,
    qtr,
    proc_days_0, proc_days_2, proc_days_3,  -- These should be 1, 11, 25
    total_business_days,                     -- This should be 61
    -- Try manual calculation
    CASE WHEN proc_days_0 >= 1 THEN (1.0 * proc_days_0) / total_business_days ELSE NULL END as manual_calc_0,
    CASE WHEN proc_days_2 >= 1 THEN (1.0 * proc_days_2) / total_business_days ELSE NULL END as manual_calc_2,
    CASE WHEN proc_days_3 >= 1 THEN (1.0 * proc_days_3) / total_business_days ELSE NULL END as manual_calc_3
FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
WHERE system_serial_number = 'SQ0093' AND qtr = '20251';

-- results

-- SYSTEM_SERIAL_NUMBER	QTR	PROC_DAYS_0	PROC_DAYS_2	PROC_DAYS_3	TOTAL_BUSINESS_DAYS	MANUAL_CALC_0	MANUAL_CALC_2	MANUAL_CALC_3
-- SQ0093	20251	1	11	25	61	0.***********	0.1803278689	0.4098360656

with diff_percents as(
select system_serial_number, qtr,
proc_days_0_percentage, proc_days_1_percentage, proc_days_10_percentage, proc_days_11_percentage, proc_days_2_percentage, proc_days_3_percentage, plus_proc_days_3_plus_percentage, proc_days_4_percentage, 
proc_days_5_percentage, 
proc_days_6_percentage, 
proc_days_7_percentage, 
proc_days_8_percentage, 
proc_days_9_percentage
from edwsbx.training.genesis_system_level_historical_chunks_bkup_qtr
except
select system_serial_number, qtr,
proc_days_0_percentage, proc_days_1_percentage, proc_days_10_percentage, proc_days_11_percentage, proc_days_2_percentage, proc_days_3_percentage, plus_proc_days_3_plus_percentage, proc_days_4_percentage, 
proc_days_5_percentage, 
proc_days_6_percentage, 
proc_days_7_percentage, 
proc_days_8_percentage, 
proc_days_9_percentage
from edwsbx.training.genesis_system_level_historical_chunks)

select system_serial_number, qtr,
proc_days_0_percentage, proc_days_1_percentage, proc_days_10_percentage, proc_days_11_percentage, proc_days_2_percentage, proc_days_3_percentage, plus_proc_days_3_plus_percentage, proc_days_4_percentage, 
proc_days_5_percentage, 
proc_days_6_percentage, 
proc_days_7_percentage, 
proc_days_8_percentage, 
proc_days_9_percentage
from edwsbx.training.genesis_system_level_historical_chunks
where system_serial_number in (select system_serial_number from diff_percents) and qtr in (select qtr from diff_percents);

-- result 19.5k rows with values - good

-- SK2526	20251	0.590164	0.295082			0.098361	0.016393	0.016393						
-- RSK8226	20251	0.131148	0.409836			0.377049	0.065574	0.081967	0.016393					
-- SK5458	20251	0.918033	0.032787			0.04918								
-- SK1386	20251	0.377049	0.196721			0.147541	0.147541	0.278689	0.098361	0.032787				
-- SK1397	20251	0.180328	0.360656			0.295082	0.147541	0.163934	0.016393					
-- SK4555	20251	0.57377	0.278689			0.081967	0.032787	0.065574	0.032787					
-- SK0456	20251	0.065574	0.377049			0.47541	0.081967	0.081967	

select procedure_number
from EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
where cal_year_qtr <= '20252'
except 
select procedure_number
from EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL_BKUP_QTR
where cal_year_qtr <= '20252';

-- results - 0 rows

select procedure_number
from EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL
where cal_year_qtr <= '20251'
except 
select procedure_number
from EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL_BKUP_QTR
where cal_year_qtr <= '20251';

-- results - 0 rows


SELECT MAX(dwload_ts) as last_refresh_date 
FROM EDW.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL;

 select system_serial_number, account_id, account_name
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20252'
except 
select system_serial_number , account_id, account_name
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20252';

-- results - 0 rows

 select system_serial_number, account_id, account_name
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20251'
except 
select system_serial_number , account_id, account_name
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20251';

-- results - 0 rows
